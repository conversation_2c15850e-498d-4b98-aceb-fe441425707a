/* ADJ Automotive Repair Services - Admin Panel CSS */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8fafc;
    color: #1f2937;
    line-height: 1.6;
}

/* Color variables */
:root {
    --primary-blue: #1e40af;
    --dark-blue: #1e3a8a;
    --light-gray: #f8fafc;
    --white: #ffffff;
    --text-gray: #6b7280;
    --border-gray: #e5e7eb;
    --success-green: #10b981;
    --warning-yellow: #fbbf24;
    --error-red: #ef4444;
}

/* Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: var(--white);
    border-right: 1px solid var(--border-gray);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Sidebar styles */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-gray);
    text-align: center;
}

.sidebar-logo {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-blue);
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--text-gray);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: var(--primary-blue);
}

.nav-item.active {
    background-color: #eff6ff;
    color: var(--primary-blue);
    border-left-color: var(--primary-blue);
}

.nav-item i {
    width: 20px;
    margin-right: 0.75rem;
}

/* Header */
.admin-header {
    background: var(--white);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: -2rem -2rem 2rem -2rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-blue);
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 600;
    color: var(--primary-blue);
}

.user-role {
    font-size: 0.875rem;
    color: var(--text-gray);
}

/* Cards */
.card {
    background: var(--white);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-gray);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-blue);
}

/* Page header */
.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.page-header p {
    color: var(--text-gray);
    font-size: 1rem;
}

/* Stats cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--white);
}

.stat-icon.blue { background-color: var(--primary-blue); }
.stat-icon.green { background-color: var(--success-green); }
.stat-icon.yellow { background-color: var(--warning-yellow); }
.stat-icon.red { background-color: var(--error-red); }

.stat-content h3 {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-gray);
    font-size: 0.875rem;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-gray);
}

.table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: var(--primary-blue);
}

.table tr:hover {
    background-color: #f9fafb;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--dark-blue);
}

.btn-secondary {
    background-color: #6b7280;
    color: var(--white);
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-success {
    background-color: var(--success-green);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-yellow);
    color: var(--white);
}

.btn-danger {
    background-color: var(--error-red);
    color: var(--white);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-blue);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-gray);
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }

.w-full { width: 100%; }

/* Responsive */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .main-content {
        order: 1;
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-header {
        margin: -1rem -1rem 1rem -1rem;
        padding: 1rem;
    }
}

/* Dashboard specific styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    border-left: 4px solid var(--primary-blue);
    padding-left: 1rem;
}

.activity-item-quote {
    border-left-color: var(--warning-yellow);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.activity-title {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
}

.activity-meta {
    font-size: 0.875rem;
    color: var(--text-gray);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-gray);
    flex-shrink: 0;
    margin-left: 1rem;
}

.activity-car {
    font-size: 0.875rem;
    color: var(--primary-blue);
}

.activity-service {
    font-size: 0.875rem;
    color: var(--warning-yellow);
    margin-bottom: 0.25rem;
}

.activity-vehicle {
    font-size: 0.75rem;
    color: var(--text-gray);
}

.no-activity {
    color: var(--text-gray);
    text-align: center;
    padding: 1rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-action-card {
    text-decoration: none;
    color: inherit;
    display: block;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.2s ease;
    border: 1px solid var(--border-gray);
}

.quick-action-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.quick-action-icon {
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
}

.quick-action-icon.add { background-color: var(--primary-blue); }
.quick-action-icon.quotes { background-color: var(--warning-yellow); }
.quick-action-icon.inquiries { background-color: var(--success-green); }
.quick-action-icon.content { background-color: var(--error-red); }


.quick-action-title {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
}

.quick-action-description {
    font-size: 0.875rem;
    color: var(--text-gray);
}

.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    font-size: 0.875rem;
}

.system-info-section h4 {
    font-weight: 600;
    color: var(--text-gray);
    margin-bottom: 0.5rem;
}

.system-info-section p {
    color: var(--text-gray);
    margin-bottom: 0.25rem;
}

.card-header-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.view-all-link {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 0.875rem;
}

.view-all-link i {
    margin-left: 0.25rem;
}

/* Updated Sidebar Styles */
.sidebar {
    width: 260px;
    background: var(--white);
    border-right: 1px solid var(--border-gray);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-gray);
    text-align: center;
}

.sidebar-logo-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--primary-blue);
}

.sidebar-logo-link i {
    font-size: 1.5rem;
}

.sidebar-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.sidebar-tagline {
    font-size: 0.75rem;
    color: var(--text-gray);
    margin-top: 0.25rem;
}

.sidebar-nav {
    flex-grow: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    text-transform: uppercase;
    color: var(--text-gray);
    font-weight: 600;
    letter-spacing: 0.05em;
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-gray);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
    font-weight: 500;
}

.nav-item:hover {
    background-color: #f3f4f6;
    color: var(--primary-blue);
}

.nav-item.active {
    background-color: #eff6ff;
    color: var(--primary-blue);
    border-left-color: var(--primary-blue);
    font-weight: 600;
}

.nav-item i {
    width: 20px;
    font-size: 1rem;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-gray);
}

.sidebar-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.sidebar-user-avatar {
    background-color: var(--primary-blue);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-user-details {
    display: flex;
    flex-direction: column;
}

.sidebar-user-name {
    font-weight: 600;
    font-size: 0.875rem;
}

.sidebar-user-role {
    font-size: 0.75rem;
    color: var(--text-gray);
}

.sidebar-footer-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.sidebar-footer-actions .btn {
    justify-content: center;
}

/* Updated Main Content & Header Styles */
.main-content {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: var(--white);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.admin-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-widget {
    position: relative;
}

.notification-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-gray);
    font-size: 1.25rem;
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background-color: var(--error-red);
    color: white;
    font-size: 0.7rem;
    font-weight: bold;
    border-radius: 50%;
    height: 18px;
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--white);
}

.page-content {
    padding: 2rem;
    flex-grow: 1;
    background-color: #f8fafc;
}

/* Form Container & Layout */
.form-container {
    background: var(--white);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 1024px) {
    .form-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.form-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-gray);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

.form-group {
    margin-bottom: 1.5rem;
}

/* Form Elements */
.form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-blue);
    font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-gray);
    border-radius: 0.375rem;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.form-input-adornment {
    position: absolute;
    left: 0;
    padding-left: 0.75rem;
    color: var(--text-gray);
    pointer-events: none;
}

.form-input-group .form-input {
    padding-left: 2rem;
}

.form-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.form-checkbox {
    height: 1.25rem;
    width: 1.25rem;
    border-radius: 0.25rem;
    border: 1px solid var(--border-gray);
    color: var(--primary-blue);
}

.form-checkbox:focus {
    ring: 2px;
    ring-offset: 2px;
    ring-color: var(--primary-blue);
}

.form-help-text {
    font-size: 0.875rem;
    color: var(--text-gray);
    margin-top: 0.5rem;
}

/* File Upload */
.file-upload-container {
    position: relative;
}

.file-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border: 2px dashed var(--border-gray);
    border-radius: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.file-upload-label:hover {
    border-color: var(--primary-blue);
}

.file-upload-label i {
    font-size: 2.5rem;
    color: var(--text-gray);
    margin-bottom: 0.75rem;
}

.file-upload-link {
    color: var(--primary-blue);
    font-weight: 600;
}

.file-upload-info {
    font-size: 0.875rem;
    color: var(--text-gray);
    margin-top: 0.25rem;
}

.file-upload-input {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
}

/* Image Preview */
.image-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.image-preview-item {
    position: relative;
    height: 120px;
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.375rem;
    border: 1px solid var(--border-gray);
}

.image-preview-item .remove-image {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Form Footer */
.form-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-gray);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Page Header for Forms */
.page-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-header-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.page-header-subtitle {
    color: var(--text-gray);
    font-size: 1rem;
    margin-top: 0.25rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 0.375rem;
    border: 1px solid transparent;
}

.alert-danger {
    background-color: #fef2f2;
    border-color: #fca5a5;
    color: #991b1b;
}

.alert-list {
    list-style-position: inside;
}

/* Filter Form */
.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: flex-end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-gray);
    vertical-align: middle;
}

.table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: var(--primary-blue);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

.table-image {
    height: 40px;
    width: 64px;
    object-fit: cover;
    border-radius: 0.25rem;
    border: 1px solid var(--border-gray);
}

.table-cell-primary {
    font-weight: 600;
    color: var(--primary-blue);
}

.table-cell-secondary {
    font-size: 0.875rem;
    color: var(--text-gray);
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.btn-icon {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-gray);
    font-size: 1rem;
    padding: 0.25rem;
    transition: color 0.2s ease;
}

.btn-icon:hover {
    color: var(--primary-blue);
}

.btn-icon.btn-icon-danger:hover {
    color: var(--error-red);
}

.btn-icon.featured {
    color: var(--warning-yellow);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.badge-available {
    background-color: #e0f2f1;
    color: #00796b;
}

.badge-sold {
    background-color: #ffebee;
    color: #c62828;
}

.badge-pending {
    background-color: #fff8e1;
    color: #f9a825;
}

.badge-featured {
    background-color: #e3f2fd;
    color: #1565c0;
    margin-left: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-state i {
    font-size: 3rem;
    color: var(--border-gray);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.empty-state-text {
    color: var(--text-gray);
    margin-bottom: 1.5rem;
}

/* Table Search */
.table-search {
    position: relative;
}

.table-search i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-gray);
}

.table-search .form-input {
    padding-left: 2.5rem;
    width: 250px;
}
