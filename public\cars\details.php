<?php
/** 
 * ADJ Automotive Repair Services - Car Details Page 
 * Individual car details with image gallery and inquiry form 
 */

// Include configuration and functions
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/navigation.php';

// Get car ID
$carId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$carId) {
    header('Location: ' . BASE_URL . '/public/cars/');
    exit;
}

// Get car details
$car = getCarById($carId);
if (!$car || $car['status'] !== 'available') {
    header('Location: ' . BASE_URL . '/public/cars/');
    exit;
}

// Increment view count
incrementCarViews($carId);

// Parse images
$images = json_decode($car['images'], true) ?: [];

// Page variables
$pageTitle = $car['year'] . ' ' . $car['make'] . ' ' . $car['model'];
$pageDescription = 'View details for this ' . $car['year'] . ' ' . $car['make'] . ' ' . $car['model'] . ' for sale at ADJ Automotive Repair Services in Guam. Price: ' . formatCurrency($car['price']);

// Get related cars (same make or similar price range)
$relatedCars = getCars([
    'status' => 'available',
    'make' => $car['make']
], 3);

// If not enough related cars by make, get by price range
if (count($relatedCars) < 3) {
    $priceMin = $car['price'] * 0.8;
    $priceMax = $car['price'] * 1.2;
    $relatedCars = getCars([
        'status' => 'available',
        'price_min' => $priceMin,
        'price_max' => $priceMax
    ], 3);
}

// Remove current car from related cars
$relatedCars = array_filter($relatedCars, function($relatedCar) use ($carId) {
    return $relatedCar['id'] != $carId;
});

// Include header
include '../../includes/header.php';
?>



<!-- Breadcrumb -->
<div class="bg-gray-50 py-4">
    <div class="container mx-auto px-4">
        <?php echo renderBreadcrumb([
            ['title' => 'Home', 'url' => BASE_URL],
            ['title' => 'Cars for Sale', 'url' => BASE_URL . '/public/cars/'],
            ['title' => $car['year'] . ' ' . $car['make'] . ' ' . $car['model']]
        ]); ?>
    </div>
</div>

<!-- Car Details Section -->
<section class="py-8 md:py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            <!-- Image Gallery -->
            <div class="lg:sticky lg:top-8">
                <?php if (!empty($images)): ?>
                <!-- Main Image -->
                <div class="mb-6">
                    <img src="<?php echo UPLOADS_URL; ?>/cars/<?php echo $images[0]; ?>"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?>"
                         class="w-full h-64 md:h-80 lg:h-96 object-cover rounded-xl shadow-lg transition-all duration-300"
                         id="main-image"
                         onerror="this.src='<?php echo ASSETS_URL; ?>/images/placeholder.png'">
                </div>

                <!-- Thumbnail Gallery -->
                <?php if (count($images) > 1): ?>
                <div class="grid grid-cols-4 md:grid-cols-5 gap-3">
                    <?php foreach ($images as $index => $image): ?>
                    <img src="<?php echo UPLOADS_URL; ?>/cars/<?php echo $image; ?>"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?> - Image <?php echo $index + 1; ?>"
                         class="w-full h-16 md:h-20 object-cover rounded-lg cursor-pointer border-2 transition-all duration-200 hover:border-primary-blue hover:-translate-y-1 hover:shadow-md <?php echo $index === 0 ? 'border-primary-blue' : 'border-transparent'; ?>"
                         data-full="<?php echo UPLOADS_URL; ?>/cars/<?php echo $image; ?>"
                         onclick="changeMainImage(this)">
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <!-- Placeholder Image -->
                <div class="mb-6">
                    <img src="<?php echo ASSETS_URL; ?>/images/placeholder.svg"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?>"
                         class="w-full h-64 md:h-80 lg:h-96 object-cover rounded-xl shadow-lg">
                </div>
                <?php endif; ?>
            </div>

            <!-- Car Information -->
            <div class="space-y-8">
                <!-- Header -->
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="mb-6">
                        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-3">
                            <?php echo htmlspecialchars($car['year'] . ' ' . $car['make'] . ' ' . $car['model']); ?>
                        </h1>
                        <?php if ($car['featured']): ?>
                        <span class="inline-flex items-center gap-1 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-star text-green-600"></i>
                            Featured
                        </span>
                        <?php endif; ?>
                    </div>

                    <div class="bg-gradient-to-r from-secondary-yellow to-yellow-500 text-primary-blue px-6 py-4 rounded-xl font-bold text-3xl md:text-4xl text-center shadow-lg mb-6">
                        <?php echo formatCurrency($car['price']); ?>
                    </div>

                    <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600 border-t pt-4">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-eye text-primary-blue"></i>
                            <span><?php echo number_format($car['views']); ?> views</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-comments text-primary-blue"></i>
                            <span><?php echo number_format($car['inquiries_count']); ?> inquiries</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-calendar text-primary-blue"></i>
                            <span>Listed <?php echo timeAgo($car['created_at']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Details -->
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <h3 class="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                        <i class="fas fa-car text-primary-blue"></i>
                        Vehicle Details
                    </h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-car text-primary-blue text-lg"></i>
                                <div>
                                    <span class="text-xs text-gray-500 uppercase tracking-wide font-medium">Make</span>
                                    <p class="font-semibold text-gray-900 text-lg"><?php echo htmlspecialchars($car['make']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-tag text-primary-blue text-lg"></i>
                                <div>
                                    <span class="text-xs text-gray-500 uppercase tracking-wide font-medium">Model</span>
                                    <p class="font-semibold text-gray-900 text-lg"><?php echo htmlspecialchars($car['model']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-calendar text-primary-blue text-lg"></i>
                                <div>
                                    <span class="text-xs text-gray-500 uppercase tracking-wide font-medium">Year</span>
                                    <p class="font-semibold text-gray-900 text-lg"><?php echo $car['year']; ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-tachometer-alt text-primary-blue text-lg"></i>
                                <div>
                                    <span class="text-xs text-gray-500 uppercase tracking-wide font-medium">Mileage</span>
                                    <p class="font-semibold text-gray-900 text-lg"><?php echo number_format($car['mileage']); ?> miles</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <?php if (!empty($car['description'])): ?>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                        <i class="fas fa-file-alt text-primary-blue"></i>
                        Description
                    </h3>
                    <div class="prose prose-gray max-w-none">
                        <p class="text-gray-700 leading-relaxed">
                            <?php echo nl2br(htmlspecialchars($car['description'])); ?>
                        </p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Quality Assurance -->
                <div class="bg-gradient-to-br from-success-green to-green-600 rounded-xl p-6 text-white shadow-lg">
                    <h3 class="text-xl font-bold mb-6 flex items-center gap-2">
                        <i class="fas fa-shield-alt"></i>
                        Quality Assurance
                    </h3>
                    <ul class="space-y-4">
                        <li class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-green-200 text-lg"></i>
                            <span>Inspected by ASE Master Certified Technicians</span>
                        </li>
                        <li class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-green-200 text-lg"></i>
                            <span>Over <?php echo BUSINESS_EXPERIENCE; ?> of automotive expertise</span>
                        </li>
                        <li class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-green-200 text-lg"></i>
                            <span>Veteran-owned business you can trust</span>
                        </li>
                        <li class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-green-200 text-lg"></i>
                            <span>Located in Yigo, Guam</span>
                        </li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="bg-white rounded-xl p-6 shadow-sm space-y-4">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Contact Us</h3>

                    <a href="<?php echo BASE_URL; ?>/public/car-inquiry.php?car_id=<?php echo $car['id']; ?>"
                       class="btn-primary w-full text-center">
                        <i class="fas fa-envelope mr-2"></i>Send Inquiry
                    </a>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <a href="<?php echo WHATSAPP_LINK; ?>" target="_blank"
                           class="inline-flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg">
                            <i class="fab fa-whatsapp"></i>WhatsApp
                        </a>

                        <a href="tel:<?php echo str_replace(['(', ')', ' ', '-'], '', BUSINESS_PHONE); ?>"
                           class="inline-flex items-center justify-center gap-2 bg-secondary-yellow hover:bg-yellow-500 text-primary-blue py-3 px-6 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg">
                            <i class="fas fa-phone"></i>Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Cars -->
<?php if (!empty($relatedCars)): ?>
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Similar Cars</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Discover other vehicles that might interest you</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach (array_slice($relatedCars, 0, 3) as $relatedCar): ?>
            <div class="card group">
                <div class="relative overflow-hidden">
                    <?php
                    $relatedImages = json_decode($relatedCar['images'], true);
                    $relatedMainImage = !empty($relatedImages) ? $relatedImages[0] : 'placeholder.svg';
                    ?>
                    <img src="<?php echo !empty($relatedImages) ? UPLOADS_URL . '/cars/' . $relatedMainImage : ASSETS_URL . '/images/placeholder.svg'; ?>"
                         alt="<?php echo htmlspecialchars($relatedCar['make'] . ' ' . $relatedCar['model']); ?>"
                         class="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                         onerror="this.src='<?php echo ASSETS_URL; ?>/images/placeholder.svg'">

                    <div class="absolute top-4 right-4 bg-secondary-yellow text-primary-blue px-3 py-2 rounded-lg font-bold shadow-lg">
                        <?php echo formatCurrency($relatedCar['price']); ?>
                    </div>
                </div>

                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4 leading-tight">
                        <?php echo htmlspecialchars($relatedCar['year'] . ' ' . $relatedCar['make'] . ' ' . $relatedCar['model']); ?>
                    </h3>

                    <div class="flex justify-between items-center text-sm text-gray-600 mb-6">
                        <div class="flex items-center gap-2">
                            <i class="fas fa-calendar text-primary-blue"></i>
                            <span><?php echo $relatedCar['year']; ?></span>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-tachometer-alt text-primary-blue"></i>
                            <span><?php echo number_format($relatedCar['mileage']); ?> miles</span>
                        </div>
                    </div>

                    <a href="details.php?id=<?php echo $relatedCar['id']; ?>"
                       class="btn-primary w-full text-center">
                        View Details
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo BASE_URL; ?>/public/cars/"
               class="inline-flex items-center gap-2 bg-white text-primary-blue border-2 border-primary-blue py-3 px-8 rounded-lg font-semibold transition-all duration-300 hover:bg-primary-blue hover:text-white hover:-translate-y-0.5 hover:shadow-lg">
                <i class="fas fa-cars"></i>
                View All Cars
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- JavaScript for Image Gallery -->
<script>
function changeMainImage(thumbnail) {
    const mainImage = document.getElementById('main-image');
    const fullImageUrl = thumbnail.getAttribute('data-full');

    if (mainImage && fullImageUrl) {
        mainImage.src = fullImageUrl;

        // Update active thumbnail - remove border from all thumbnails
        document.querySelectorAll('[data-full]').forEach(thumb => {
            thumb.classList.remove('border-primary-blue');
            thumb.classList.add('border-transparent');
        });

        // Add border to clicked thumbnail
        thumbnail.classList.remove('border-transparent');
        thumbnail.classList.add('border-primary-blue');
    }
}

// Initialize gallery
document.addEventListener('DOMContentLoaded', function() {
    const thumbnails = document.querySelectorAll('[data-full]');
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            changeMainImage(this);
        });
    });

    // Add smooth loading for images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.style.opacity = '0';
        img.addEventListener('load', function() {
            this.style.transition = 'opacity 0.3s ease';
            this.style.opacity = '1';
        });

        // If image is already loaded (cached)
        if (img.complete) {
            img.style.opacity = '1';
        }
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
