<?php
/** 
 * ADJ Automotive Repair Services - Car Details Page 
 * Individual car details with image gallery and inquiry form 
 */

// Include configuration and functions
require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/navigation.php';

// Get car ID
$carId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$carId) {
    header('Location: ' . BASE_URL . '/public/cars/');
    exit;
}

// Get car details
$car = getCarById($carId);
if (!$car || $car['status'] !== 'available') {
    header('Location: ' . BASE_URL . '/public/cars/');
    exit;
}

// Increment view count
incrementCarViews($carId);

// Parse images
$images = json_decode($car['images'], true) ?: [];

// Page variables
$pageTitle = $car['year'] . ' ' . $car['make'] . ' ' . $car['model'];
$pageDescription = 'View details for this ' . $car['year'] . ' ' . $car['make'] . ' ' . $car['model'] . ' for sale at ADJ Automotive Repair Services in Guam. Price: ' . formatCurrency($car['price']);

// Get related cars (same make or similar price range)
$relatedCars = getCars([
    'status' => 'available',
    'make' => $car['make']
], 3);

// If not enough related cars by make, get by price range
if (count($relatedCars) < 3) {
    $priceMin = $car['price'] * 0.8;
    $priceMax = $car['price'] * 1.2;
    $relatedCars = getCars([
        'status' => 'available',
        'price_min' => $priceMin,
        'price_max' => $priceMax
    ], 3);
}

// Remove current car from related cars
$relatedCars = array_filter($relatedCars, function($relatedCar) use ($carId) {
    return $relatedCar['id'] != $carId;
});

// Include header
include '../../includes/header.php';
?>

<style>
/* Enhanced styles for better UI/UX */
.car-gallery {
    position: sticky;
    top: 2rem;
}

.main-image {
    transition: all 0.3s ease;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.thumbnail {
    transition: all 0.2s ease;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
}

.thumbnail:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.thumbnail.active {
    border-color: #3B82F6;
    transform: translateY(-2px);
}

.vehicle-detail-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.detail-item:hover {
    transform: translateY(-1px);
}

.price-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.quality-card {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.action-btn {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 48px; /* Better touch target */
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.btn-call {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.related-car-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.related-car-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .car-gallery {
        position: static;
        margin-bottom: 2rem;
    }
    
    .main-image {
        height: 250px;
    }
    
    .thumbnail {
        height: 60px;
    }
    
    .price-badge {
        font-size: 1.5rem;
        padding: 0.5rem 1rem;
    }
    
    .detail-item {
        padding: 0.5rem;
    }
    
    .action-btn {
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
    }
}
</style>

<!-- Breadcrumb -->
<div class="bg-gray-50 py-4">
    <div class="container mx-auto px-4">
        <?php echo renderBreadcrumb([
            ['title' => 'Home', 'url' => BASE_URL],
            ['title' => 'Cars for Sale', 'url' => BASE_URL . '/public/cars/'],
            ['title' => $car['year'] . ' ' . $car['make'] . ' ' . $car['model']]
        ]); ?>
    </div>
</div>

<!-- Car Details Section -->
<section class="py-6 md:py-12">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            <!-- Image Gallery -->
            <div class="car-gallery">
                <?php if (!empty($images)): ?>
                <!-- Main Image -->
                <div class="mb-4 md:mb-6">
                    <img src="<?php echo UPLOADS_URL; ?>/cars/<?php echo $images[0]; ?>"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?>"
                         class="main-image w-full h-64 md:h-96 object-cover"
                         onerror="this.src='<?php echo ASSETS_URL; ?>/images/placeholder.png'">
                </div>
                
                <!-- Thumbnail Gallery -->
                <?php if (count($images) > 1): ?>
                <div class="grid grid-cols-4 md:grid-cols-5 gap-2 md:gap-3">
                    <?php foreach ($images as $index => $image): ?>
                    <img src="<?php echo UPLOADS_URL; ?>/cars/<?php echo $image; ?>"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?> - Image <?php echo $index + 1; ?>"
                         class="thumbnail w-full h-16 md:h-20 object-cover <?php echo $index === 0 ? 'active' : ''; ?>"
                         data-full="<?php echo UPLOADS_URL; ?>/cars/<?php echo $image; ?>"
                         onclick="changeMainImage(this)">
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <!-- Placeholder Image -->
                <div class="mb-4 md:mb-6">
                    <img src="<?php echo ASSETS_URL; ?>/images/placeholder.svg"
                         alt="<?php echo htmlspecialchars($car['make'] . ' ' . $car['model']); ?>"
                         class="main-image w-full h-64 md:h-96 object-cover">
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Car Information -->
            <div class="space-y-6">
                <!-- Header -->
                <div>
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                        <div>
                            <h1 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 leading-tight">
                                <?php echo htmlspecialchars($car['year'] . ' ' . $car['make'] . ' ' . $car['model']); ?>
                            </h1>
                            <?php if ($car['featured']): ?>
                            <span class="inline-block mt-2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                ⭐ Featured
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="price-badge inline-block mb-4">
                        <?php echo formatCurrency($car['price']); ?>
                    </div>
                    
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                        <div class="stats-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo $car['views']; ?> views</span>
                        </div>
                        <div class="stats-item">
                            <i class="fas fa-comments"></i>
                            <span><?php echo $car['inquiries_count']; ?> inquiries</span>
                        </div>
                        <div class="stats-item">
                            <i class="fas fa-calendar"></i>
                            <span>Listed <?php echo timeAgo($car['created_at']); ?></span>
                        </div>
                    </div>
                </div>
                
                <!-- Key Details -->
                <div class="vehicle-detail-card">
                    <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-4">Vehicle Details</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                        <div class="detail-item">
                            <i class="fas fa-car text-blue-600 text-lg mr-3"></i>
                            <div>
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Make</span>
                                <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($car['make']); ?></p>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <i class="fas fa-tag text-blue-600 text-lg mr-3"></i>
                            <div>
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Model</span>
                                <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($car['model']); ?></p>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <i class="fas fa-calendar text-blue-600 text-lg mr-3"></i>
                            <div>
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Year</span>
                                <p class="font-semibold text-gray-900"><?php echo $car['year']; ?></p>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <i class="fas fa-tachometer-alt text-blue-600 text-lg mr-3"></i>
                            <div>
                                <span class="text-xs text-gray-500 uppercase tracking-wide">Mileage</span>
                                <p class="font-semibold text-gray-900"><?php echo number_format($car['mileage']); ?> miles</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Description -->
                <?php if (!empty($car['description'])): ?>
                <div class="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
                    <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-3">Description</h3>
                    <p class="text-gray-700 leading-relaxed">
                        <?php echo nl2br(htmlspecialchars($car['description'])); ?>
                    </p>
                </div>
                <?php endif; ?>
                
                <!-- Quality Assurance -->
                <div class="quality-card">
                    <h3 class="text-lg md:text-xl font-bold mb-4">
                        <i class="fas fa-shield-alt mr-2"></i>Quality Assurance
                    </h3>
                    <ul class="space-y-3 text-sm md:text-base">
                        <li class="flex items-center">
                            <i class="fas fa-check mr-3 text-green-200"></i>
                            <span>Inspected by ASE Master Certified Technicians</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check mr-3 text-green-200"></i>
                            <span>Over <?php echo BUSINESS_EXPERIENCE; ?> of automotive expertise</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check mr-3 text-green-200"></i>
                            <span>Veteran-owned business you can trust</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check mr-3 text-green-200"></i>
                            <span>Located in Yigo, Guam</span>
                        </li>
                    </ul>
                </div>
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    <a href="<?php echo BASE_URL; ?>/public/car-inquiry.php?car_id=<?php echo $car['id']; ?>"
                       class="action-btn btn-primary w-full">
                        <i class="fas fa-envelope mr-2"></i>Send Inquiry
                    </a>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
                        <a href="<?php echo WHATSAPP_LINK; ?>" target="_blank"
                           class="action-btn btn-whatsapp">
                            <i class="fab fa-whatsapp mr-2"></i>WhatsApp
                        </a>
                        
                        <a href="tel:<?php echo str_replace(['(', ')', ' ', '-'], '', BUSINESS_PHONE); ?>"
                           class="action-btn btn-call">
                            <i class="fas fa-phone mr-2"></i>Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Cars -->
<?php if (!empty($relatedCars)): ?>
<section class="py-12 md:py-16 bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="container mx-auto px-4">
        <div class="text-center mb-8 md:mb-12">
            <h2 class="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Similar Cars</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Discover other vehicles that might interest you</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            <?php foreach (array_slice($relatedCars, 0, 3) as $relatedCar): ?>
            <div class="related-car-card">
                <div class="relative">
                    <?php
                    $relatedImages = json_decode($relatedCar['images'], true);
                    $relatedMainImage = !empty($relatedImages) ? $relatedImages[0] : 'placeholder.svg';
                    ?>
                    <img src="<?php echo !empty($relatedImages) ? UPLOADS_URL . '/cars/' . $relatedMainImage : ASSETS_URL . '/images/placeholder.svg'; ?>"
                         alt="<?php echo htmlspecialchars($relatedCar['make'] . ' ' . $relatedCar['model']); ?>"
                         class="w-full h-48 md:h-56 object-cover"
                         onerror="this.src='<?php echo ASSETS_URL; ?>/images/placeholder.svg'">
                    
                    <div class="absolute top-4 right-4 bg-white px-3 py-2 rounded-lg font-bold text-blue-600 shadow-lg">
                        <?php echo formatCurrency($relatedCar['price']); ?>
                    </div>
                </div>
                
                <div class="p-6">
                    <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-3 leading-tight">
                        <?php echo htmlspecialchars($relatedCar['year'] . ' ' . $relatedCar['make'] . ' ' . $relatedCar['model']); ?>
                    </h3>
                    
                    <div class="flex justify-between items-center text-sm text-gray-600 mb-4">
                        <div class="stats-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo $relatedCar['year']; ?></span>
                        </div>
                        <div class="stats-item">
                            <i class="fas fa-tachometer-alt"></i>
                            <span><?php echo number_format($relatedCar['mileage']); ?> miles</span>
                        </div>
                    </div>
                    
                    <a href="details.php?id=<?php echo $relatedCar['id']; ?>" 
                       class="action-btn btn-primary w-full">
                        View Details
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-8 md:mt-12">
            <a href="<?php echo BASE_URL; ?>/public/cars/" 
               class="action-btn bg-white text-gray-900 border-2 border-gray-300 hover:border-gray-400">
                View All Cars
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- JavaScript for Image Gallery -->
<script>
function changeMainImage(thumbnail) {
    const mainImage = document.querySelector('.main-image');
    const fullImageUrl = thumbnail.getAttribute('data-full');
    
    if (mainImage && fullImageUrl) {
        mainImage.src = fullImageUrl;
        
        // Update active thumbnail
        document.querySelectorAll('.thumbnail').forEach(thumb => {
            thumb.classList.remove('active');
        });
        thumbnail.classList.add('active');
    }
}

// Initialize gallery
document.addEventListener('DOMContentLoaded', function() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            changeMainImage(this);
        });
    });
    
    // Add loading states for images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
