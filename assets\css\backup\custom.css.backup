/* ADJ Automotive Repair Services - Custom CSS */

/* Professional Blue & Yellow Palette Variables */
:root {
    --primary-blue: #1e40af;        /* Professional Blue - Primary brand color */
    --secondary-yellow: #fbbf24;    /* Bright Yellow - Accent color */
    --light-gray: #f8fafc;         /* Very Light Gray - Clean backgrounds */
    --white: #ffffff;              /* Pure White - Clean backgrounds */
    --dark-blue: #1e3a8a;          /* Darker Blue - Headers and emphasis */
    --text-gray: #64748b;          /* Medium Gray - Body text */
    --border-gray: #e2e8f0;        /* Light Gray - Borders and dividers */

    /* Legacy variables (mapped to new palette for compatibility) */
    --secondary-orange: #fbbf24;    /* Mapped to yellow */
    --accent-red: #1e40af;          /* Mapped to primary blue */
    --dark-gray: #1e3a8a;           /* Mapped to dark blue */
    --success-green: #10b981;       /* Green for success states */
    --warning-yellow: #fbbf24;      /* Yellow for warnings */
    --black: #0f172a;              /* Near black for high contrast text */
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e3a8a;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Remove focus outline for buttons and links when clicked, but keep for keyboard navigation */
button:focus:not(:focus-visible),
a:focus:not(:focus-visible),
.btn-primary:focus:not(:focus-visible),
.btn-secondary:focus:not(:focus-visible),
.whatsapp-button:focus:not(:focus-visible) {
    outline: none;
}

/* Keep focus visible for keyboard navigation */
button:focus-visible,
a:focus-visible,
.btn-primary:focus-visible,
.btn-secondary:focus-visible,
.whatsapp-button:focus-visible {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Skip to content link */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* Header dropdown animation */
.group:hover .group-hover\:opacity-100 {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.group .group-hover\:opacity-100 {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

/* Button hover effects */
.btn-primary {
    background-color: var(--primary-blue);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

.btn-secondary {
    background-color: var(--secondary-yellow);
    color: var(--dark-blue);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: #f59e0b;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
}

/* Card hover effects */
.card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Image gallery styles */
.image-gallery {
    position: relative;
}

.image-gallery img {
    transition: transform 0.3s ease;
}

.image-gallery:hover img {
    transform: scale(1.05);
}

/* Loading spinner */
.spinner {
    border: 3px solid var(--light-gray);
    border-top: 3px solid var(--primary-blue);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form styles */
.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus {
    border-color: var(--primary-blue);
    outline: none;
}

.form-input.error {
    border-color: var(--accent-red);
}

.form-label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.form-error {
    color: var(--accent-red);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Badge styles */
.badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-success {
    background-color: var(--success-green);
    color: white;
}

.badge-warning {
    background-color: var(--warning-yellow);
    color: white;
}

.badge-error {
    background-color: var(--accent-red);
    color: white;
}

.badge-info {
    background-color: var(--primary-blue);
    color: white;
}

/* Veteran badge special styling */
.veteran-badge {
    background: linear-gradient(45deg, var(--accent-red), #b91c1c);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
    position: relative;
    overflow: hidden;
}

.veteran-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.veteran-badge:hover::before {
    left: 100%;
}

/* Floating elements */
.floating {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Pulse animation for important elements */
.pulse-slow {
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .mobile-center {
        text-align: center;
    }
    .mobile-full {
        width: 100%;
    }
    .mobile-stack {
        flex-direction: column;
    }
    .mobile-hide {
        display: none !important;
    }
    /* Hide all floating buttons on mobile */
    .floating, .phone-float, .phone-floating-button, .floating.phone, .floating.call, .floating.phone-float, .floating.call-float, .floating.phone-btn, .floating.call-btn, .floating.floating-phone, .floating.floating-call, .floating.floating-call-btn, .floating.floating-phone-btn {
        display: none !important;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn-primary, .btn-secondary {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here if needed */
}

/* Custom utility classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Professional typography improvements */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

/* Enhanced form styling for the service request */
.service-form-input {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.service-form-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--secondary-yellow);
    box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

.service-form-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Full-screen hero section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* Background image styling */
.hero-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.15;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.9), rgba(30, 58, 138, 0.8));
    z-index: 2;
}

/* Ensure hero content is above overlay */
.hero-section .container {
    z-index: 10;
    position: relative;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-yellow), #f59e0b);
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-blue), var(--secondary-gray)) 1;
}

/* Image optimization */
img {
    max-width: 100%;
    height: auto;
}

.lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Simplified WhatsApp button styling */

.whatsapp-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background-color: #25D366;
    color: white;
    border-radius: 50%;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease-in-out;
    text-decoration: none;
}

.whatsapp-button:hover {
    background-color: #128C7E;
    transform: scale(1.1);
}

.whatsapp-button i {
    font-size: 24px;
}

/* Legacy WhatsApp float styling for compatibility */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    animation: none;
    transform: scale(1.1);
}

/* Service icons */
.service-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.service-icon:hover {
    background: var(--secondary-gray);
    transform: scale(1.1);
}

/* About Section Utility Classes */
.text-center {
    text-align: center;
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.text-gray-800 {
    color: #1f2937;
}

.text-gray-600 {
    color: #4b5563;
}

.text-gray-700 {
    color: #374151;
}

.text-primary-blue {
    color: var(--primary-blue);
}

.text-green-500 {
    color: #10b981;
}

.text-green-800 {
    color: #065f46;
}

.text-blue-800 {
    color: #1e40af;
}

.bg-white {
    background-color: white;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-green-100 {
    background-color: #dcfce7;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded {
    border-radius: 0.25rem;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.p-8 {
    padding: 2rem;
}

.p-6 {
    padding: 1.5rem;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.pt-8 {
    padding-top: 2rem;
}

.max-w-3xl {
    max-width: 48rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-8 {
    gap: 2rem;
}

.gap-2 {
    gap: 0.5rem;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-200 {
    border-color: #e5e7eb;
}

.italic {
    font-style: italic;
}

/* Large screen styles */
@media (min-width: 1024px) {
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\:col-span-1 {
        grid-column: span 1 / span 1;
    }

    .lg\:col-span-2 {
        grid-column: span 2 / span 2;
    }
}

/* Medium screen styles */
@media (min-width: 768px) {
    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }
}

/* Professional Homepage Styles - Minimalist Design */

/* Hero Section */
.hero-section {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  position: relative;
  display: flex;
  align-items: center;
  color: white;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  inset: 0;
  z-index: 1;
  background: url('../images/background.jpg') center/cover no-repeat;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    rgba(30, 64, 175, 0.85) 0%,
    rgba(71, 85, 105, 0.8) 30%,
    rgba(30, 58, 138, 0.85) 70%,
    rgba(59, 130, 246, 0.8) 100%);
}

/* Hero Content */
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
  margin-bottom: 2rem;
}

.hero-subtitle {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
}

.hero-main {
  display: block;
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 700;
  line-height: 1.1;
  color: white;
}

.hero-main .highlight {
  color: #fbbf24;
}

.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Trust Indicators */
.trust-indicators {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 3rem;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.trust-item i {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(251, 191, 36, 0.2);
  border: 1px solid #fbbf24;
  border-radius: 50%;
  color: #fbbf24;
  font-size: 0.9rem;
}

.trust-item span {
  font-weight: 600;
  color: white;
}

/* Action Buttons */
.hero-actions {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #fbbf24;
  color: #1e40af;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
}

.btn-primary:hover {
  background: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(251, 191, 36, 0.4);
}

.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    min-height: 100vh;
    padding: 2rem 0;
  }

  .hero-content {
    max-width: 100%;
    padding: 0 1rem;
  }

  .hero-main {
    font-size: clamp(2rem, 8vw, 2.5rem);
  }

  .hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .trust-indicators {
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .trust-item {
    font-size: 0.9rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
    padding: 0.875rem 1.5rem;
  }
}




/* Services Section */
.services-section {
  padding: 3rem 0;
  background: #f8fafc;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.section-title i {
  font-size: 1.5rem;
}

.section-description {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.service-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-top: 4px solid #1e40af;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: #1e40af;
  color: white;
  border-radius: 8px;
  font-size: 1.2rem;
  margin: 0 auto 1rem;
}

.service-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.75rem;
}

.service-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.service-pricing {
  margin-bottom: 1rem;
}

.service-price {
  font-size: 1rem;
  font-weight: 600;
  color: #1e40af;
  display: block;
}

.service-rate {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.service-bonus {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: #fef3c7;
  color: #92400e;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.service-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #1e40af;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.service-btn:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.no-services {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.no-services i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #9ca3af;
}

.section-action {
  text-align: center;
}

.btn-view-all {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #1e40af;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-view-all:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

/* Cars Section */
.cars-section {
  padding: 3rem 0;
  background: #f1f5f9;
}

.cars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.car-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-top: 4px solid #1e40af;
}

.car-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.car-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.car-price-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.95);
  color: #1e40af;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.car-content {
  padding: 1.5rem;
}

.car-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.75rem;
}

.car-specs {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.car-spec {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #64748b;
  font-size: 0.8rem;
}

.car-spec i {
  font-size: 0.7rem;
}

.car-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.car-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.car-btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #1e40af;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.trust-indicators {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
}

/* Mobile menu fixes */
#mobile-menu {
    z-index: 50;
    position: relative;
    transition: all 0.3s ease-in-out;
}

#mobile-menu.hidden {
    display: none !important;
}

#mobile-menu:not(.hidden) {
    display: block !important;
}

@media (max-width: 768px) {
    #mobile-menu-button {
        z-index: 51;
        transition: all 0.2s ease;
    }

    #mobile-menu-button:hover {
        transform: scale(1.05);
    }

    /* Ensure mobile menu is visible when not hidden */
    #mobile-menu {
        width: 100%;
        background: white;
        border-top: 1px solid #e5e7eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
}



.logo-bg-fixed {
  width: 90px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}
