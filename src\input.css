@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f8fafc;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #1e40af;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #1e3a8a;
  }
  
  /* Focus styles for accessibility */
  *:focus {
    outline: 2px solid #1e40af;
    outline-offset: 2px;
  }
  
  /* Remove focus outline for buttons and links when clicked, but keep for keyboard navigation */
  button:focus:not(:focus-visible),
  a:focus:not(:focus-visible) {
    outline: none;
  }
  
  /* Keep focus visible for keyboard navigation */
  button:focus-visible,
  a:focus-visible {
    outline: 2px solid #1e40af;
    outline-offset: 2px;
  }
}

/* Custom component styles */
@layer components {
  /* Button Components */
  .btn-primary {
    @apply inline-flex items-center gap-2 bg-primary-blue text-white py-3 px-6 rounded-lg font-semibold text-base no-underline transition-all duration-300 ease-in-out shadow-lg hover:bg-blue-700 hover:-translate-y-0.5 hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply inline-flex items-center gap-2 bg-transparent text-white py-3 px-6 border-2 border-white/30 rounded-lg font-semibold text-base no-underline transition-all duration-300 ease-in-out hover:bg-white/10 hover:border-white/50 hover:-translate-y-0.5;
  }
  
  .btn-yellow {
    @apply inline-flex items-center gap-2 bg-secondary-yellow text-primary-blue py-4 px-8 rounded-lg font-semibold text-base no-underline transition-all duration-300 ease-in-out shadow-lg shadow-yellow-400/30 hover:bg-yellow-500 hover:-translate-y-0.5 hover:shadow-xl hover:shadow-yellow-400/40;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-sm transition-all duration-300 ease-in-out overflow-hidden hover:-translate-y-1 hover:shadow-lg-hover;
  }
  
  /* Form Components */
  .form-input {
    @apply w-full px-3 py-3 border-2 border-gray-300 rounded-lg text-base transition-colors duration-300 focus:border-primary-blue focus:outline-none;
  }
  
  .form-input.error {
    @apply border-red-500;
  }
  
  .form-label {
    @apply block font-semibold mb-2 text-gray-700;
  }
  
  .form-error {
    @apply text-red-500 text-sm mt-1;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-block py-1 px-3 rounded-full text-xs font-semibold uppercase tracking-wider;
  }
  
  .badge-success {
    @apply bg-success-green text-white;
  }
  
  .badge-warning {
    @apply bg-warning-yellow text-white;
  }
  
  .badge-error {
    @apply bg-error-red text-white;
  }
  
  .badge-info {
    @apply bg-primary-blue text-white;
  }
  
  /* Section Components */
  .section-header {
    @apply text-center mb-8 max-w-2xl mx-auto;
  }
  
  .section-title {
    @apply flex items-center justify-center gap-2 text-3xl font-bold text-primary-blue mb-2;
  }
  
  .section-description {
    @apply text-lg text-text-gray leading-relaxed;
  }
  
  /* Service Card Components */
  .service-card {
    @apply bg-white rounded-lg p-6 shadow-sm transition-all duration-300 border-t-4 border-primary-blue text-center hover:-translate-y-0.5 hover:shadow-lg;
  }
  
  .service-icon {
    @apply flex items-center justify-center w-12 h-12 bg-primary-blue text-white rounded-lg text-xl mx-auto mb-4;
  }
  
  .service-title {
    @apply text-lg font-semibold text-primary-blue mb-3;
  }
  
  .service-description {
    @apply text-sm text-text-gray leading-relaxed mb-4;
  }
  
  .service-btn {
    @apply inline-flex items-center gap-2 bg-primary-blue text-white py-2 px-4 rounded-md text-sm font-semibold no-underline transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5;
  }
  
  /* Car Card Components */
  .car-card {
    @apply bg-white rounded-lg overflow-hidden shadow-sm transition-all duration-300 border-t-4 border-primary-blue hover:-translate-y-0.5 hover:shadow-lg;
  }
  
  .car-image-container {
    @apply relative h-48 overflow-hidden;
  }
  
  .car-image {
    @apply w-full h-full object-cover;
  }
  
  .car-price-badge {
    @apply absolute top-4 right-4 bg-white/95 text-primary-blue py-2 px-3 rounded-md font-bold text-sm shadow-sm;
  }
  
  .car-content {
    @apply p-6;
  }
  
  .car-title {
    @apply text-lg font-semibold text-primary-blue mb-3;
  }
  
  .car-specs {
    @apply flex gap-4 mb-3;
  }
  
  .car-spec {
    @apply flex items-center gap-1 text-text-gray text-sm;
  }
  
  .car-description {
    @apply text-sm text-text-gray leading-relaxed mb-4;
  }
  
  .car-btn-primary {
    @apply inline-flex items-center gap-2 bg-primary-blue text-white py-2 px-4 rounded-md text-sm font-semibold no-underline transition-all duration-300 flex-1 justify-center hover:bg-blue-700 hover:-translate-y-0.5;
  }
  
  /* WhatsApp Button */
  .whatsapp-button {
    @apply flex items-center justify-center w-11 h-11 bg-green-500 text-white rounded-full shadow-lg transition-all duration-300 no-underline hover:bg-green-600 hover:scale-110;
  }
  
  .whatsapp-float {
    @apply fixed bottom-5 right-5 z-50 animate-pulse-slow hover:animate-none hover:scale-110;
  }
  
  /* Grid Layouts */
  .services-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8;
  }
  
  .cars-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8;
  }
  
  /* View All Button */
  .btn-view-all {
    @apply inline-flex items-center gap-2 bg-primary-blue text-white py-3 px-6 rounded-md text-sm font-semibold no-underline transition-all duration-300 hover:bg-blue-700 hover:-translate-y-0.5;
  }
  
  /* Loading Spinner */
  .spinner {
    @apply border-4 border-light-gray border-t-primary-blue rounded-full w-10 h-10 animate-spin;
  }
  
  /* Screen Reader Only */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
  }
  
  .sr-only:focus {
    @apply static w-auto h-auto p-2 m-0 overflow-visible whitespace-normal;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
  }
  
  .bg-gradient-secondary {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
  }
  
  .hero-gradient {
    background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.85) 0%,
      rgba(71, 85, 105, 0.8) 30%,
      rgba(30, 58, 138, 0.85) 70%,
      rgba(59, 130, 246, 0.8) 100%);
  }
  
  /* Mobile specific utilities */
  @media (max-width: 768px) {
    .mobile-center {
      text-align: center;
    }
    .mobile-full {
      width: 100%;
    }
    .mobile-stack {
      flex-direction: column;
    }
    .mobile-hide {
      display: none !important;
    }
  }
  
  /* Print utilities */
  @media print {
    .no-print {
      display: none !important;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .card {
      border: 2px solid #000;
    }
    
    .btn-primary, .btn-secondary {
      border: 2px solid #000;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Quote Request Page Specific Styles */
@layer components {
  /* Quote Page Layout */
  .quote-page {
    @apply min-h-screen bg-light-gray flex flex-col justify-start pb-0 mb-0;
  }

  /* Quote Header */
  .quote-header {
    @apply bg-gradient-primary py-6 text-white;
  }

  .header-content {
    @apply text-center max-w-2xl mx-auto;
  }

  .page-title {
    @apply text-3xl font-bold mb-1;
  }

  .page-subtitle {
    @apply text-base opacity-90;
  }

  /* Form Layout */
  .form-layout {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto items-start;
  }

  .form-main {
    @apply lg:col-span-2;
  }

  .form-card {
    @apply bg-white rounded-xl p-8 shadow-lg border-t-4 border-primary-blue border border-gray-200 transition-all duration-300 animate-slideInUp hover:shadow-xl hover:-translate-y-0.5;
  }

  /* Form Sections */
  .form-section {
    @apply mb-6 pb-4 border-b border-gray-100 w-full max-w-full overflow-hidden last:border-b-0 last:mb-0 last:pb-0;
  }

  .form-section h3 {
    @apply flex items-center gap-3 text-lg font-bold text-primary-blue mb-5 pb-2 border-b-2 border-gray-100;
  }

  .form-section h3 i {
    @apply text-base text-blue-500;
  }

  /* Form Grid */
  .form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-full;
  }

  .form-grid.three-columns {
    @apply grid-cols-1 md:grid-cols-3 max-w-full;
  }

  .form-group.full-width {
    @apply col-span-full;
  }

  .form-group {
    @apply mb-4;
  }

  .form-group label {
    @apply block text-sm font-semibold text-gray-700 mb-2;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    @apply w-full max-w-full px-4 py-3.5 bg-white border-2 border-gray-300 rounded-lg text-base text-gray-700 transition-all duration-300 box-border hover:border-gray-400 hover:shadow-sm focus:outline-none focus:bg-white focus:border-blue-500 focus:shadow-lg focus:shadow-blue-500/10 focus:-translate-y-0.5;
    font-family: inherit;
  }

  .form-group input::placeholder,
  .form-group textarea::placeholder {
    @apply text-gray-400 italic;
  }

  .form-group select {
    @apply cursor-pointer appearance-none bg-no-repeat pr-12;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-size: 1.25em 1.25em;
  }

  .form-group textarea {
    @apply resize-y min-h-[120px] leading-relaxed;
  }

  /* Submit Section */
  .form-submit {
    @apply text-center pt-8 border-t-2 border-gray-100 mb-0;
  }

  .submit-btn {
    @apply inline-flex items-center gap-3 bg-gradient-to-r from-secondary-yellow to-yellow-500 text-primary-blue py-4 px-10 border-0 rounded-xl text-base font-bold cursor-pointer transition-all duration-300 shadow-lg shadow-yellow-400/30 uppercase tracking-wider hover:from-yellow-500 hover:to-yellow-600 hover:-translate-y-0.5 hover:shadow-xl hover:shadow-yellow-400/40;
  }

  .submit-note {
    @apply mt-4 text-sm text-text-gray italic;
  }

  /* Sidebar */
  .sidebar {
    @apply flex flex-col gap-5;
  }

  .sidebar-card {
    @apply bg-white rounded-lg p-5 shadow-sm border-t-4 border-primary-blue;
  }

  .sidebar-card h3 {
    @apply flex items-center gap-2 text-base font-semibold text-primary-blue mb-3;
  }

  .sidebar-card h3 i {
    @apply text-sm;
  }

  /* Contact List */
  .contact-list {
    @apply flex flex-col gap-2.5;
  }

  .contact-item {
    @apply flex items-center gap-2.5 p-2.5 bg-gray-50 rounded-md no-underline text-inherit transition-all duration-300 hover:bg-gray-100 hover:translate-x-1;
  }

  .contact-item i {
    @apply w-7 h-7 flex items-center justify-center rounded-md text-white text-sm;
  }

  .contact-item .fa-phone {
    @apply bg-primary-blue;
  }

  .contact-item .fa-whatsapp {
    @apply bg-green-500;
  }

  .contact-item .fa-envelope {
    @apply bg-indigo-500;
  }

  .contact-item div {
    @apply flex flex-col;
  }

  .contact-item strong {
    @apply text-sm text-gray-700;
  }

  .contact-item span {
    @apply text-xs text-text-gray;
  }

  /* Business Hours */
  .hours-list {
    @apply flex flex-col gap-1.5;
  }

  .hours-item {
    @apply flex justify-between items-center py-2 px-2.5 bg-gray-50 rounded-md text-sm;
  }

  .hours-item span:first-child {
    @apply font-semibold text-gray-700;
  }

  .hours-item span:last-child {
    @apply text-green-500;
  }

  .hours-item span.closed {
    @apply text-red-500;
  }

  /* Features List */
  .features-list {
    @apply list-none p-0 m-0 flex flex-col gap-2.5;
  }

  .features-list li {
    @apply flex items-center gap-2.5 py-2 px-2.5 bg-gray-50 rounded-md text-sm font-medium text-gray-700 transition-all duration-300 hover:bg-gray-100 hover:translate-x-1;
  }

  .features-list li i {
    @apply w-5 h-5 flex items-center justify-center rounded text-white text-xs;
  }

  .features-list li .fa-certificate {
    @apply bg-primary-blue;
  }

  .features-list li .fa-medal {
    @apply bg-secondary-yellow;
  }

  .features-list li .fa-clock {
    @apply bg-green-500;
  }

  .features-list li .fa-shield-alt {
    @apply bg-red-500;
  }

  .features-list li .fa-gift {
    @apply bg-purple-500;
  }

  /* Success Page */
  .success-section {
    @apply py-12;
  }

  .success-card {
    @apply max-w-lg mx-auto bg-white rounded-lg p-8 shadow-lg text-center border-t-4 border-green-500;
  }

  .success-icon {
    @apply inline-flex items-center justify-center w-16 h-16 bg-green-500 text-white rounded-full text-2xl mb-4;
  }

  .success-card h2 {
    @apply text-2xl font-bold text-primary-blue mb-4;
  }

  .success-card p {
    @apply text-text-gray mb-8 leading-relaxed;
  }

  .contact-options {
    @apply flex gap-4 justify-center mb-8;
  }

  .contact-btn {
    @apply inline-flex items-center gap-2 py-3 px-6 rounded-md font-semibold no-underline transition-all duration-300 text-sm hover:-translate-y-0.5 hover:shadow-lg;
  }

  .contact-btn:first-child {
    @apply bg-primary-blue text-white;
  }

  .contact-btn.whatsapp {
    @apply bg-green-500 text-white;
  }

  .back-link {
    @apply inline-flex items-center gap-2 text-text-gray no-underline text-sm transition-colors duration-300 hover:text-primary-blue;
  }

  /* Error Alert */
  .error-alert {
    @apply flex items-start gap-4 bg-red-50 border border-red-200 rounded-md p-4 mb-6 text-red-600;
  }

  .error-alert i {
    @apply text-red-600 mt-1;
  }

  .error-alert ul {
    @apply list-none p-0 mt-2 mb-0;
  }

  .error-alert li {
    @apply text-sm mb-1 before:content-['•'] before:mr-2;
  }
}
